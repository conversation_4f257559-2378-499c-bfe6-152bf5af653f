/**
 * Modern Hero Section JavaScript
 * For WHMCS WIDDX Template
 * Professional animations and interactions
 */

document.addEventListener('DOMContentLoaded', function() {
    // Initialize AOS animations if available
    if (typeof AOS !== 'undefined') {
        AOS.init({
            duration: 1000,
            easing: 'ease-out-cubic',
            once: true,
            offset: 100
        });
    }

    // Simple hover effects for benefit items
    const benefitItems = document.querySelectorAll('.benefit-item');
    if (benefitItems.length > 0) {
        benefitItems.forEach((item, index) => {
            // Staggered animation on load
            setTimeout(() => {
                item.style.opacity = '1';
                item.style.transform = 'translateY(0)';
            }, 500 + (index * 100));

            // Set initial state
            item.style.opacity = '0';
            item.style.transform = 'translateY(20px)';
            item.style.transition = 'all 0.5s ease';
        });
    }

    // Main server animation
    const mainServer = document.querySelector('.main-server');
    if (mainServer) {
        setTimeout(() => {
            mainServer.style.opacity = '1';
            mainServer.style.transform = 'translateY(0)';
        }, 300);

        // Set initial state
        mainServer.style.opacity = '0';
        mainServer.style.transform = 'translateY(30px)';
        mainServer.style.transition = 'all 0.6s ease';
    }

    // Smooth scroll for hero buttons
    const heroButtons = document.querySelectorAll('.btn-modern[href^="#"]');
    heroButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            const targetId = this.getAttribute('href').substring(1);
            const targetElement = document.getElementById(targetId);
            
            if (targetElement) {
                targetElement.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });

    // Parallax effect for background elements
    let ticking = false;
    
    function updateParallax() {
        const scrolled = window.pageYOffset;
        const parallaxElements = document.querySelectorAll('.gradient-orb, .particle');
        
        parallaxElements.forEach((element, index) => {
            const speed = 0.5 + (index * 0.1);
            const yPos = -(scrolled * speed);
            element.style.transform = `translateY(${yPos}px)`;
        });
        
        ticking = false;
    }
    
    function requestTick() {
        if (!ticking) {
            requestAnimationFrame(updateParallax);
            ticking = true;
        }
    }
    
    // Only add scroll listener if hero section exists
    if (document.querySelector('.modern-hero-section')) {
        window.addEventListener('scroll', requestTick);
    }

    // Trust indicators counter animation
    const trustNumbers = document.querySelectorAll('.trust-number');
    const observerOptions = {
        threshold: 0.5,
        rootMargin: '0px 0px -100px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const element = entry.target;
                const finalValue = element.textContent;
                
                // Extract number from text (e.g., "99.9%" -> 99.9)
                const numericValue = parseFloat(finalValue.replace(/[^\d.]/g, ''));
                
                if (!isNaN(numericValue)) {
                    animateCounter(element, 0, numericValue, finalValue);
                }
                
                observer.unobserve(element);
            }
        });
    }, observerOptions);

    trustNumbers.forEach(number => {
        observer.observe(number);
    });

    function animateCounter(element, start, end, finalText) {
        const duration = 2000;
        const startTime = performance.now();
        
        function updateCounter(currentTime) {
            const elapsed = currentTime - startTime;
            const progress = Math.min(elapsed / duration, 1);
            
            // Easing function
            const easeOutCubic = 1 - Math.pow(1 - progress, 3);
            const current = start + (end - start) * easeOutCubic;
            
            // Format the number based on original format
            if (finalText.includes('%')) {
                element.textContent = current.toFixed(1) + '%';
            } else if (finalText.includes('K')) {
                element.textContent = Math.floor(current) + 'K+';
            } else if (finalText.includes('/')) {
                element.textContent = Math.floor(current) + '/7';
            } else {
                element.textContent = Math.floor(current);
            }
            
            if (progress < 1) {
                requestAnimationFrame(updateCounter);
            } else {
                element.textContent = finalText;
            }
        }
        
        requestAnimationFrame(updateCounter);
    }

    // Button ripple effect
    const modernButtons = document.querySelectorAll('.btn-modern');
    modernButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            const ripple = this.querySelector('.btn-ripple');
            if (ripple) {
                ripple.style.transform = 'scale(0)';
                setTimeout(() => {
                    ripple.style.transform = 'scale(1)';
                }, 10);
                
                setTimeout(() => {
                    ripple.style.transform = 'scale(0)';
                }, 600);
            }
        });
    });

    // Feature items hover enhancement
    const featureItems = document.querySelectorAll('.feature-item');
    featureItems.forEach(item => {
        item.addEventListener('mouseenter', function() {
            this.style.transform = 'translateX(10px) scale(1.02)';
        });
        
        item.addEventListener('mouseleave', function() {
            this.style.transform = 'translateX(0) scale(1)';
        });
    });

    // Scroll indicator functionality
    const scrollIndicator = document.querySelector('.scroll-indicator');
    if (scrollIndicator) {
        scrollIndicator.addEventListener('click', function() {
            const heroHeight = document.querySelector('.modern-hero-section').offsetHeight;
            window.scrollTo({
                top: heroHeight,
                behavior: 'smooth'
            });
        });

        // Hide scroll indicator when user scrolls
        window.addEventListener('scroll', function() {
            const scrolled = window.pageYOffset;
            if (scrolled > 100) {
                scrollIndicator.style.opacity = '0';
                scrollIndicator.style.pointerEvents = 'none';
            } else {
                scrollIndicator.style.opacity = '1';
                scrollIndicator.style.pointerEvents = 'auto';
            }
        });
    }
});

// Performance optimization: Reduce motion for users who prefer it
if (window.matchMedia('(prefers-reduced-motion: reduce)').matches) {
    document.addEventListener('DOMContentLoaded', function() {
        const style = document.createElement('style');
        style.textContent = `
            .modern-hero-section *,
            .modern-hero-section *::before,
            .modern-hero-section *::after {
                animation-duration: 0.01ms !important;
                animation-iteration-count: 1 !important;
                transition-duration: 0.01ms !important;
            }
        `;
        document.head.appendChild(style);
    });
}
