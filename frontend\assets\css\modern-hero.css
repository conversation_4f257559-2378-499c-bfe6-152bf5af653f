/**
 * Modern Hero Section CSS
 * Professional and Sophisticated Design
 * Company Brand Colors: Purple (#4a338d) and Magenta (#cc00bb)
 */

/* Import company brand colors and variables */
@import url('variables.css');

/* ===================================
   MODERN HERO SECTION
   =================================== */

.modern-hero-section {
    background: transparent;
    position: relative;
    min-height: 100vh;
    display: flex;
    align-items: center;
    padding: 120px 0 80px;
    overflow: hidden;
}

/* Remove any background overrides for dark theme */
[data-bs-theme="dark"] .modern-hero-section {
    background: transparent;
}

/* ===================================
   SUBTLE BACKGROUND ELEMENTS
   =================================== */

.hero-bg-elements {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 0;
    opacity: 0.4;
}

.gradient-orb {
    position: absolute;
    border-radius: 50%;
    background: radial-gradient(circle, var(--primary-color) 0%, transparent 70%);
    opacity: 0.1;
    animation: pulse 4s ease-in-out infinite;
}

.orb-1 {
    width: 300px;
    height: 300px;
    top: 10%;
    left: -10%;
    animation-delay: 0s;
}

.orb-2 {
    width: 200px;
    height: 200px;
    top: 60%;
    right: -5%;
    background: radial-gradient(circle, var(--secondary-color) 0%, transparent 70%);
    animation-delay: 2s;
}

.orb-3 {
    width: 150px;
    height: 150px;
    top: 30%;
    right: 20%;
    background: radial-gradient(circle, var(--primary-light) 0%, transparent 70%);
    animation-delay: 1s;
}

.floating-particles {
    position: absolute;
    width: 100%;
    height: 100%;
}

.particle {
    position: absolute;
    width: 4px;
    height: 4px;
    background: var(--primary-color);
    border-radius: 50%;
    opacity: 0.6;
    animation: floatParticle 8s linear infinite;
}

.particle-1 { top: 20%; left: 15%; animation-delay: 0s; }
.particle-2 { top: 40%; left: 80%; animation-delay: 2s; }
.particle-3 { top: 70%; left: 25%; animation-delay: 4s; }
.particle-4 { top: 15%; left: 70%; animation-delay: 6s; }
.particle-5 { top: 85%; left: 60%; animation-delay: 1s; }

@keyframes pulse {
    0%, 100% { transform: scale(1); opacity: 0.1; }
    50% { transform: scale(1.1); opacity: 0.2; }
}

@keyframes floatParticle {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    25% { transform: translateY(-10px) rotate(90deg); }
    50% { transform: translateY(-5px) rotate(180deg); }
    75% { transform: translateY(-15px) rotate(270deg); }
}

/* ===================================
   HERO CONTENT SECTION
   =================================== */

.hero-content-section {
    position: relative;
    z-index: 2;
}

.hero-content {
    padding: 2rem 0;
}

/* Hero Badge */
.hero-badge {
    display: inline-flex;
    align-items: center;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: var(--white);
    padding: 8px 20px;
    border-radius: 30px;
    font-size: 14px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    position: relative;
    overflow: hidden;
    box-shadow: var(--shadow-lg);
}

.badge-glow {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, var(--primary-light), var(--secondary-light));
    opacity: 0;
    transition: opacity 0.3s ease;
}

.hero-badge:hover .badge-glow {
    opacity: 0.3;
}

/* Hero Title */
.hero-title {
    font-size: 3.5rem;
    font-weight: 800;
    line-height: 1.2;
    color: var(--text-primary);
    margin-bottom: 2rem;
}

.title-line-1,
.title-line-3 {
    display: block;
    color: var(--text-primary);
}

.title-line-2 {
    display: block;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Hero Description */
.hero-description {
    font-size: 1.2rem;
    color: var(--text-secondary);
    line-height: 1.7;
    max-width: 500px;
}

/* Hero Features */
.hero-features {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.feature-item {
    display: flex;
    align-items: center;
    padding: 1rem;
    background: var(--bg-card);
    border-radius: 12px;
    border: 1px solid var(--border-color);
    transition: all 0.3s ease;
    box-shadow: var(--shadow-sm);
}

.feature-item:hover {
    transform: translateX(10px);
    box-shadow: var(--shadow-md);
    border-color: var(--primary-color);
}

.feature-icon {
    width: 50px;
    height: 50px;
    border-radius: 12px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--white);
    font-size: 1.2rem;
    margin-right: 1rem;
    flex-shrink: 0;
}

.feature-content {
    flex-grow: 1;
}

.feature-title {
    font-size: 1rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 0.25rem;
}

.feature-desc {
    font-size: 0.9rem;
    color: var(--text-secondary);
    margin: 0;
}

/* Hero Actions */
.hero-actions {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.btn-modern {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 1rem 2rem;
    border-radius: 16px;
    font-weight: 600;
    font-size: 1rem;
    text-decoration: none;
    position: relative;
    overflow: hidden;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    border: none;
    cursor: pointer;
    min-width: 180px;
}

.btn-modern .btn-text {
    position: relative;
    z-index: 2;
    transition: transform 0.3s ease;
}

.btn-modern .btn-icon {
    margin-left: 0.5rem;
    transition: transform 0.3s ease;
    position: relative;
    z-index: 2;
}

.btn-modern:hover .btn-text {
    transform: translateX(-5px);
}

.btn-modern:hover .btn-icon {
    transform: translateX(5px);
}

.btn-ripple {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.3) 0%, transparent 70%);
    transform: scale(0);
    transition: transform 0.6s ease;
    z-index: 1;
}

.btn-modern:hover .btn-ripple {
    transform: scale(1);
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
    color: var(--white);
    box-shadow: 0 8px 25px rgba(74, 51, 141, 0.3);
}

.btn-primary:hover {
    transform: translateY(-3px);
    box-shadow: 0 15px 35px rgba(74, 51, 141, 0.4);
    color: var(--white);
}

.btn-secondary {
    background: transparent;
    color: var(--text-primary);
    border: 2px solid var(--border-color);
}

.btn-secondary:hover {
    background: linear-gradient(135deg, var(--secondary-color), var(--secondary-light));
    color: var(--white);
    border-color: var(--secondary-color);
    transform: translateY(-3px);
    box-shadow: 0 15px 35px rgba(204, 0, 187, 0.3);
}

/* Trust Indicators */
.trust-indicators {
    display: flex;
    gap: 2rem;
    padding-top: 2rem;
    border-top: 1px solid var(--border-color);
}

.trust-item {
    text-align: center;
}

.trust-number {
    font-size: 1.8rem;
    font-weight: 800;
    color: var(--primary-color);
    line-height: 1;
}

.trust-label {
    font-size: 0.9rem;
    color: var(--text-secondary);
    margin-top: 0.25rem;
}

/* ===================================
   HERO VISUAL SECTION
   =================================== */

.hero-visual-section {
    position: relative;
    z-index: 1;
}

.hero-visual {
    position: relative;
    height: 500px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.visual-container {
    position: relative;
    width: 100%;
    height: 100%;
}

/* Server Stack */
.server-stack {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.server-unit {
    width: 200px;
    height: 60px;
    background: linear-gradient(135deg, var(--gray-100), var(--gray-200));
    border-radius: 8px;
    border: 2px solid var(--border-color);
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 1rem;
    position: relative;
    box-shadow: var(--shadow-md);
    transition: all 0.3s ease;
}

.server-unit:hover {
    transform: scale(1.05);
    box-shadow: var(--shadow-lg);
}

.server-lights {
    display: flex;
    gap: 0.5rem;
}

.light {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: var(--gray-400);
    transition: all 0.3s ease;
}

.light.active {
    background: var(--success-color);
    box-shadow: 0 0 10px var(--success-color);
    animation: blink 2s ease-in-out infinite;
}

.server-label {
    font-size: 0.8rem;
    font-weight: 600;
    color: var(--text-secondary);
}

@keyframes blink {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

/* Floating Cards */
.floating-cards {
    position: absolute;
    width: 100%;
    height: 100%;
}

.floating-card {
    position: absolute;
    background: var(--bg-card);
    border: 1px solid var(--border-color);
    border-radius: 12px;
    padding: 1rem;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
    box-shadow: var(--shadow-md);
    opacity: 0;
    transform: translateY(20px);
    transition: all 0.3s ease;
}

.floating-card i {
    font-size: 1.5rem;
    color: var(--primary-color);
}

.floating-card span {
    font-size: 0.8rem;
    font-weight: 600;
    color: var(--text-secondary);
    white-space: nowrap;
}

.card-1 { top: 10%; left: 10%; }
.card-2 { top: 20%; right: 15%; }
.card-3 { bottom: 25%; left: 5%; }
.card-4 { bottom: 15%; right: 10%; }

@keyframes floatUp {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

/* Connection Lines */
.connection-lines {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
}

.connection-svg {
    width: 100%;
    height: 100%;
}

.connection-path {
    stroke-dasharray: 1000;
    stroke-dashoffset: 1000;
}

/* ===================================
   SCROLL INDICATOR
   =================================== */

.scroll-indicator {
    position: absolute;
    bottom: 2rem;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
    color: var(--text-secondary);
    animation: bounce 2s ease-in-out infinite;
}

.scroll-mouse {
    width: 24px;
    height: 40px;
    border: 2px solid var(--text-secondary);
    border-radius: 12px;
    position: relative;
}

.scroll-wheel {
    width: 4px;
    height: 8px;
    background: var(--text-secondary);
    border-radius: 2px;
    position: absolute;
    top: 6px;
    left: 50%;
    transform: translateX(-50%);
    animation: scrollWheel 2s ease-in-out infinite;
}

.scroll-text {
    font-size: 0.8rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

@keyframes bounce {
    0%, 100% { transform: translateX(-50%) translateY(0); }
    50% { transform: translateX(-50%) translateY(-10px); }
}

@keyframes scrollWheel {
    0% { top: 6px; opacity: 1; }
    100% { top: 20px; opacity: 0; }
}

/* ===================================
   RESPONSIVE DESIGN
   =================================== */

@media (max-width: 1200px) {
    .hero-title {
        font-size: 3rem;
    }
    
    .trust-indicators {
        gap: 1.5rem;
    }
}

@media (max-width: 992px) {
    .modern-hero-section {
        padding: 100px 0 60px;
    }
    
    .hero-title {
        font-size: 2.5rem;
    }
    
    .hero-visual {
        height: 400px;
        margin-top: 3rem;
    }
    
    .hero-actions {
        justify-content: center;
    }
    
    .trust-indicators {
        justify-content: center;
    }
}

@media (max-width: 768px) {
    .hero-title {
        font-size: 2rem;
        text-align: center;
    }
    
    .hero-description {
        text-align: center;
        margin: 0 auto 2rem;
    }
    
    .hero-actions {
        flex-direction: column;
        align-items: center;
    }
    
    .btn-modern {
        width: 100%;
        max-width: 280px;
    }
    
    .trust-indicators {
        gap: 1rem;
        flex-wrap: wrap;
    }
    
    .hero-visual {
        height: 300px;
    }
    
    .server-unit {
        width: 150px;
        height: 50px;
    }
    
    .floating-card {
        padding: 0.75rem;
    }
}

@media (max-width: 576px) {
    .modern-hero-section {
        padding: 80px 0 40px;
    }
    
    .hero-title {
        font-size: 1.8rem;
    }
    
    .hero-description {
        font-size: 1rem;
    }
    
    .hero-features {
        gap: 0.75rem;
    }
    
    .feature-item {
        padding: 0.75rem;
    }
    
    .feature-icon {
        width: 40px;
        height: 40px;
        font-size: 1rem;
    }
}

/* Dark Theme Adjustments */
[data-bs-theme="dark"] .server-unit {
    background: linear-gradient(135deg, var(--gray-800), var(--gray-700));
    border-color: var(--gray-600);
}

[data-bs-theme="dark"] .floating-card {
    background: var(--gray-800);
    border-color: var(--gray-700);
}

[data-bs-theme="dark"] .feature-item {
    background: var(--gray-800);
    border-color: var(--gray-700);
}

[data-bs-theme="dark"] .btn-secondary {
    border-color: var(--gray-600);
    color: var(--text-primary);
}
