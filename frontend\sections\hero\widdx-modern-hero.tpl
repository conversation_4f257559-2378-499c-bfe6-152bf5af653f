<!-- Modern Hero Section with Transparent Background -->
<section class="modern-hero-section position-relative">
    <!-- Subtle Background Elements -->
    <div class="hero-bg-elements">
        <div class="gradient-orb orb-1"></div>
        <div class="gradient-orb orb-2"></div>
        <div class="gradient-orb orb-3"></div>
        <div class="floating-particles">
            <div class="particle particle-1"></div>
            <div class="particle particle-2"></div>
            <div class="particle particle-3"></div>
            <div class="particle particle-4"></div>
            <div class="particle particle-5"></div>
        </div>
    </div>

    <div class="container position-relative">
        <div class="row align-items-center min-vh-100 py-5">
            <!-- Left Section - Content -->
            <div class="col-lg-6 col-md-12 hero-content-section" data-aos="fade-right" data-aos-duration="1000">
                <div class="hero-content">
                    <!-- Hero Badge -->
                    <div class="hero-badge mb-4">
                        <i class="fas fa-rocket me-2"></i>
                        <span>Premium Web Hosting</span>
                        <div class="badge-glow"></div>
                    </div>

                    <!-- Main Heading -->
                    <h1 class="hero-title mb-4">
                        <span class="title-line-1">Powerful & Reliable</span>
                        <span class="title-line-2 gradient-text">Web Hosting Solutions</span>
                        <span class="title-line-3">For Your Business</span>
                    </h1>

                    <!-- Hero Description -->
                    <p class="hero-description mb-5">
                        Experience lightning-fast performance, 99.9% uptime, and world-class support with our premium hosting solutions designed for modern businesses.
                    </p>

                    <!-- Key Features List -->
                    <div class="hero-features mb-5">
                        <div class="feature-item">
                            <div class="feature-icon">
                                <i class="fas fa-bolt"></i>
                            </div>
                            <div class="feature-content">
                                <h6 class="feature-title">Lightning Fast</h6>
                                <p class="feature-desc">SSD storage and optimized servers for maximum speed</p>
                            </div>
                        </div>
                        <div class="feature-item">
                            <div class="feature-icon">
                                <i class="fas fa-shield-alt"></i>
                            </div>
                            <div class="feature-content">
                                <h6 class="feature-title">Secure & Protected</h6>
                                <p class="feature-desc">Advanced security features and SSL certificates included</p>
                            </div>
                        </div>
                        <div class="feature-item">
                            <div class="feature-icon">
                                <i class="fas fa-headset"></i>
                            </div>
                            <div class="feature-content">
                                <h6 class="feature-title">24/7 Expert Support</h6>
                                <p class="feature-desc">Round-the-clock technical support from hosting experts</p>
                            </div>
                        </div>
                    </div>

                    <!-- Action Buttons -->
                    <div class="hero-actions">
                        <a href="#pricing" class="btn-modern btn-primary">
                            <span class="btn-text">Get Started Now</span>
                            <i class="fas fa-arrow-right btn-icon"></i>
                            <div class="btn-ripple"></div>
                        </a>
                        <a href="#features" class="btn-modern btn-secondary">
                            <span class="btn-text">View Features</span>
                            <i class="fas fa-play btn-icon"></i>
                            <div class="btn-ripple"></div>
                        </a>
                    </div>

                    <!-- Trust Indicators -->
                    <div class="trust-indicators mt-5">
                        <div class="trust-item">
                            <div class="trust-number">99.9%</div>
                            <div class="trust-label">Uptime Guarantee</div>
                        </div>
                        <div class="trust-item">
                            <div class="trust-number">24/7</div>
                            <div class="trust-label">Expert Support</div>
                        </div>
                        <div class="trust-item">
                            <div class="trust-number">10K+</div>
                            <div class="trust-label">Happy Customers</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Right Section - Visual -->
            <div class="col-lg-6 col-md-12 hero-visual-section" data-aos="fade-left" data-aos-duration="1000" data-aos-delay="200">
                <div class="hero-visual">
                    <!-- Main Visual Container -->
                    <div class="visual-container">
                        <!-- Server Illustration -->
                        <div class="server-stack">
                            <div class="server-unit server-1">
                                <div class="server-lights">
                                    <div class="light active"></div>
                                    <div class="light active"></div>
                                    <div class="light"></div>
                                </div>
                                <div class="server-label">Web Server</div>
                            </div>
                            <div class="server-unit server-2">
                                <div class="server-lights">
                                    <div class="light active"></div>
                                    <div class="light active"></div>
                                    <div class="light active"></div>
                                </div>
                                <div class="server-label">Database</div>
                            </div>
                            <div class="server-unit server-3">
                                <div class="server-lights">
                                    <div class="light active"></div>
                                    <div class="light"></div>
                                    <div class="light active"></div>
                                </div>
                                <div class="server-label">CDN</div>
                            </div>
                        </div>

                        <!-- Floating Cards -->
                        <div class="floating-cards">
                            <div class="floating-card card-1">
                                <i class="fas fa-rocket"></i>
                                <span>Fast Loading</span>
                            </div>
                            <div class="floating-card card-2">
                                <i class="fas fa-lock"></i>
                                <span>SSL Secure</span>
                            </div>
                            <div class="floating-card card-3">
                                <i class="fas fa-cloud"></i>
                                <span>Cloud Backup</span>
                            </div>
                            <div class="floating-card card-4">
                                <i class="fas fa-chart-line"></i>
                                <span>Analytics</span>
                            </div>
                        </div>

                        <!-- Connection Lines -->
                        <div class="connection-lines">
                            <svg class="connection-svg" viewBox="0 0 400 300">
                                <path class="connection-path" d="M50,150 Q200,50 350,150" stroke="url(#gradient1)" stroke-width="2" fill="none"/>
                                <path class="connection-path" d="M50,150 Q200,250 350,150" stroke="url(#gradient2)" stroke-width="2" fill="none"/>
                                <defs>
                                    <linearGradient id="gradient1" x1="0%" y1="0%" x2="100%" y2="0%">
                                        <stop offset="0%" style="stop-color:var(--primary-color);stop-opacity:0.8"/>
                                        <stop offset="100%" style="stop-color:var(--secondary-color);stop-opacity:0.8"/>
                                    </linearGradient>
                                    <linearGradient id="gradient2" x1="0%" y1="0%" x2="100%" y2="0%">
                                        <stop offset="0%" style="stop-color:var(--secondary-color);stop-opacity:0.6"/>
                                        <stop offset="100%" style="stop-color:var(--primary-color);stop-opacity:0.6"/>
                                    </linearGradient>
                                </defs>
                            </svg>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Scroll Indicator -->
    <div class="scroll-indicator">
        <div class="scroll-mouse">
            <div class="scroll-wheel"></div>
        </div>
        <span class="scroll-text">Scroll Down</span>
    </div>
</section>

<!-- AOS Animation Library -->
<link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">
<script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>

<script>
// Initialize AOS animations
document.addEventListener('DOMContentLoaded', function() {
    AOS.init({
        duration: 1000,
        easing: 'ease-out-cubic',
        once: true,
        offset: 100
    });

    // Animate connection lines
    const paths = document.querySelectorAll('.connection-path');
    paths.forEach((path, index) => {
        const length = path.getTotalLength();
        path.style.strokeDasharray = length;
        path.style.strokeDashoffset = length;
        
        setTimeout(() => {
            path.style.transition = 'stroke-dashoffset 2s ease-in-out';
            path.style.strokeDashoffset = 0;
        }, 1500 + (index * 500));
    });

    // Floating cards animation
    const floatingCards = document.querySelectorAll('.floating-card');
    floatingCards.forEach((card, index) => {
        setTimeout(() => {
            card.style.animation = `floatUp 0.8s ease-out forwards, float 3s ease-in-out infinite ${index * 0.5}s`;
        }, 2000 + (index * 200));
    });
});
</script>
