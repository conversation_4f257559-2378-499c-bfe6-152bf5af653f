/**
 * Modern Tools Section CSS
 * Professional and Advanced Design for Free Website Tools
 * Company Brand Colors: Purple (#4a338d) and Magenta (#cc00bb)
 */

/* Import company brand colors and variables */
@import url('variables.css');

/* ===================================
   MODERN TOOLS SECTION
   =================================== */

.modern-tools-section {
    background: transparent;
    position: relative;
    padding: 80px 0;
}

/* Remove any background overrides for dark theme */
[data-bs-theme="dark"] .modern-tools-section {
    background: transparent;
}

/* ===================================
   SUBTLE BACKGROUND ELEMENTS
   =================================== */

.tools-bg-elements {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 0;
    opacity: 0.3;
}

.floating-shape {
    position: absolute;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    opacity: 0.03;
    animation: float 8s ease-in-out infinite;
}

.shape-1 {
    width: 120px;
    height: 120px;
    top: 15%;
    left: 8%;
    animation-delay: 0s;
}

.shape-2 {
    width: 80px;
    height: 80px;
    top: 70%;
    right: 15%;
    animation-delay: 3s;
}

.shape-3 {
    width: 60px;
    height: 60px;
    top: 40%;
    right: 25%;
    animation-delay: 6s;
}

.shape-4 {
    width: 90px;
    height: 90px;
    bottom: 25%;
    left: 20%;
    animation-delay: 1.5s;
}

@keyframes float {
    0%, 100% {
        transform: translateY(0px) rotate(0deg);
    }
    50% {
        transform: translateY(-10px) rotate(90deg);
    }
}

/* ===================================
   SECTION HEADER
   =================================== */

.section-badge {
    display: inline-flex;
    align-items: center;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: var(--white);
    padding: 6px 16px;
    border-radius: 25px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    box-shadow: var(--shadow-sm);
}

.section-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    line-height: 1.3;
    color: var(--text-primary);
}

.gradient-text {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.section-subtitle {
    font-size: 1.1rem;
    color: var(--text-secondary);
    max-width: 600px;
    line-height: 1.6;
    margin-bottom: 1.5rem;
}

.title-decoration {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    margin-top: 2rem;
}

.decoration-line {
    width: 60px;
    height: 2px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-radius: 2px;
}

.decoration-dot {
    width: 8px;
    height: 8px;
    background: var(--secondary-color);
    border-radius: 50%;
}

/* ===================================
   MODERN TOOL CARDS
   =================================== */

.tools-grid {
    position: relative;
    z-index: 1;
}

.modern-tool-card {
    background: var(--bg-card);
    border-radius: 16px;
    padding: 0;
    border: 1px solid var(--border-color);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    height: 100%;
    display: flex;
    flex-direction: column;
    box-shadow: var(--shadow-sm);
}

.modern-tool-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: -1;
}

.modern-tool-card:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-lg);
    border-color: var(--primary-color);
}

.modern-tool-card:hover::before {
    opacity: 0.03;
}

[data-bs-theme="dark"] .modern-tool-card {
    background: var(--gray-800);
    border-color: var(--gray-700);
}

[data-bs-theme="dark"] .modern-tool-card:hover {
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.3);
}

/* ===================================
   TOOL CARD HEADER
   =================================== */

.tool-card-header {
    position: relative;
    padding: 1.5rem 1.5rem 1rem;
    text-align: center;
}

.tool-icon-wrapper {
    position: relative;
    display: inline-block;
    margin-bottom: 1rem;
}

.tool-icon {
    width: 70px;
    height: 70px;
    border-radius: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.8rem;
    color: var(--white);
    position: relative;
    z-index: 2;
    transition: all 0.3s ease;
}

.seo-icon {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
}

.whois-icon {
    background: linear-gradient(135deg, var(--secondary-color), var(--secondary-light));
}

.sitemap-icon {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
}

.icon-glow {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 100px;
    height: 100px;
    border-radius: 50%;
    background: radial-gradient(circle, var(--primary-color) 0%, transparent 70%);
    opacity: 0;
    transition: all 0.4s ease;
    z-index: 1;
}

.modern-tool-card:hover .icon-glow {
    opacity: 0.2;
    transform: translate(-50%, -50%) scale(1.1);
}

.modern-tool-card:hover .tool-icon {
    transform: scale(1.05) rotate(3deg);
}

.tool-badge {
    position: absolute;
    top: 1rem;
    right: 1rem;
    padding: 4px 12px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.tool-badge.popular {
    background: linear-gradient(135deg, var(--warning-color), var(--warning-light));
    color: var(--white);
}

.tool-badge.new {
    background: linear-gradient(135deg, var(--success-color), var(--success-light));
    color: var(--white);
}

.tool-badge.trending {
    background: linear-gradient(135deg, var(--secondary-color), var(--secondary-light));
    color: var(--white);
}

/* ===================================
   TOOL CARD BODY
   =================================== */

.tool-card-body {
    padding: 0 1.5rem 1rem;
    flex-grow: 1;
    display: flex;
    flex-direction: column;
}

.tool-title {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 1rem;
    text-align: center;
}

.tool-description {
    color: var(--text-secondary);
    font-size: 1rem;
    line-height: 1.6;
    text-align: center;
    margin-bottom: 1.5rem;
}

.tool-features {
    flex-grow: 1;
}

.feature-item {
    display: flex;
    align-items: center;
    margin-bottom: 0.75rem;
    padding: 0.5rem;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.feature-item:hover {
    background: var(--bg-secondary);
    transform: translateX(5px);
}

.feature-item i {
    color: var(--success-color);
    margin-right: 0.75rem;
    font-size: 1rem;
    width: 16px;
    text-align: center;
}

.feature-item span {
    color: var(--text-secondary);
    font-size: 0.9rem;
    line-height: 1.4;
}

/* ===================================
   TOOL CARD FOOTER
   =================================== */

.tool-card-footer {
    padding: 1rem 1.5rem 1.5rem;
}

.modern-btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    padding: 1rem 2rem;
    border-radius: 16px;
    font-weight: 600;
    font-size: 1rem;
    text-decoration: none;
    position: relative;
    overflow: hidden;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    border: none;
    cursor: pointer;
}

.modern-btn .btn-text {
    position: relative;
    z-index: 2;
    transition: transform 0.3s ease;
}

.modern-btn .btn-icon {
    margin-left: 0.5rem;
    transition: transform 0.3s ease;
    position: relative;
    z-index: 2;
}

.modern-btn:hover .btn-text {
    transform: translateX(-5px);
}

.modern-btn:hover .btn-icon {
    transform: translateX(5px);
}

.btn-ripple {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.3) 0%, transparent 70%);
    transform: scale(0);
    transition: transform 0.6s ease;
    z-index: 1;
}

.modern-btn:hover .btn-ripple {
    transform: scale(1);
}

.primary-btn {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
    color: var(--white);
    box-shadow: 0 8px 20px rgba(74, 51, 141, 0.3);
}

.primary-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 12px 30px rgba(74, 51, 141, 0.4);
    color: var(--white);
}

.secondary-btn {
    background: linear-gradient(135deg, var(--secondary-color), var(--secondary-light));
    color: var(--white);
    box-shadow: 0 8px 20px rgba(204, 0, 187, 0.3);
}

.secondary-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 12px 30px rgba(204, 0, 187, 0.4);
    color: var(--white);
}

.accent-btn {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: var(--white);
    box-shadow: 0 8px 20px rgba(74, 51, 141, 0.3);
}

.accent-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 12px 30px rgba(74, 51, 141, 0.4);
    color: var(--white);
}

/* ===================================
   WHY CHOOSE SECTION
   =================================== */

.why-choose-section {
    background: linear-gradient(135deg, var(--bg-secondary) 0%, var(--bg-tertiary) 100%);
    border-radius: 24px;
    padding: 4rem 2rem;
    position: relative;
    border: 1px solid var(--border-color);
    margin-top: 3rem;
    overflow: hidden;
}

.why-choose-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    opacity: 0.02;
    z-index: 0;
}

[data-bs-theme="dark"] .why-choose-section {
    background: var(--gray-800);
    border-color: var(--gray-700);
}

.why-choose-section .section-content {
    position: relative;
    z-index: 1;
}

.why-choose-section .section-title {
    color: var(--text-primary);
    font-size: 2rem;
    margin-bottom: 1rem;
}

.why-choose-section .section-subtitle {
    color: var(--text-secondary);
    font-size: 1rem;
    margin-bottom: 2.5rem;
}

.feature-highlight {
    text-align: center;
    padding: 2rem 1.5rem;
    border-radius: 20px;
    background: var(--bg-card);
    border: 1px solid var(--border-color);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    height: 100%;
    box-shadow: var(--shadow-md);
    position: relative;
    overflow: hidden;
}

.feature-highlight::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: 0;
}

.feature-highlight:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: var(--shadow-xl);
    border-color: var(--primary-color);
}

.feature-highlight:hover::before {
    opacity: 0.05;
}

.feature-highlight > * {
    position: relative;
    z-index: 1;
}

.feature-icon {
    width: 80px;
    height: 80px;
    border-radius: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    font-size: 2rem;
    color: var(--white);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
}

.feature-icon::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border-radius: 20px;
    background: inherit;
    opacity: 0.3;
    transform: scale(0);
    transition: all 0.4s ease;
}

.speed-icon {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
    box-shadow: 0 8px 20px rgba(74, 51, 141, 0.3);
}

.security-icon {
    background: linear-gradient(135deg, var(--secondary-color), var(--secondary-light));
    box-shadow: 0 8px 20px rgba(204, 0, 187, 0.3);
}

.free-icon {
    background: linear-gradient(135deg, var(--primary-dark), var(--primary-color));
    box-shadow: 0 8px 20px rgba(58, 40, 112, 0.3);
}

.mobile-icon {
    background: linear-gradient(135deg, var(--secondary-dark), var(--secondary-color));
    box-shadow: 0 8px 20px rgba(163, 0, 154, 0.3);
}

.feature-highlight:hover .feature-icon {
    transform: scale(1.15) rotate(8deg);
}

.feature-highlight:hover .feature-icon::after {
    transform: scale(1.5);
    opacity: 0.2;
}

.feature-title {
    color: var(--text-primary);
    font-size: 1.2rem;
    font-weight: 700;
    margin-bottom: 1rem;
    transition: color 0.3s ease;
}

.feature-highlight:hover .feature-title {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.feature-desc {
    color: var(--text-secondary);
    font-size: 1rem;
    line-height: 1.6;
    margin: 0;
    transition: color 0.3s ease;
}

.feature-highlight:hover .feature-desc {
    color: var(--text-primary);
}

/* ===================================
   RESPONSIVE DESIGN
   =================================== */

/* Large screens */
@media (max-width: 1200px) {
    .section-title {
        font-size: 2.25rem;
    }

    .floating-shape {
        opacity: 0.02;
    }
}

/* Medium screens */
@media (max-width: 992px) {
    .modern-tools-section {
        padding: 60px 0;
    }

    .section-title {
        font-size: 2rem;
    }

    .section-subtitle {
        font-size: 1rem;
    }

    .tool-card-header {
        padding: 1.25rem 1.25rem 0.75rem;
    }

    .tool-card-body {
        padding: 0 1.25rem 0.75rem;
    }

    .tool-card-footer {
        padding: 0.75rem 1.25rem 1.25rem;
    }

    .why-choose-section {
        padding: 2.5rem 1.5rem;
    }
}

/* Small screens */
@media (max-width: 768px) {
    .modern-tools-section {
        padding: 50px 0;
    }

    .section-title {
        font-size: 1.75rem;
    }

    .section-subtitle {
        font-size: 0.95rem;
        max-width: 100%;
    }

    .floating-shape {
        display: none;
    }

    .tool-icon {
        width: 60px;
        height: 60px;
        font-size: 1.5rem;
    }

    .icon-glow {
        width: 80px;
        height: 80px;
    }

    .tool-title {
        font-size: 1.2rem;
    }

    .modern-btn {
        padding: 0.75rem 1.25rem;
        font-size: 0.9rem;
    }

    .why-choose-section {
        border-radius: 16px;
        padding: 2rem 1.25rem;
    }

    .why-choose-section .section-title {
        font-size: 1.75rem;
    }

    .feature-highlight {
        padding: 1.25rem 0.75rem;
        margin-bottom: 1rem;
    }

    .feature-icon {
        width: 50px;
        height: 50px;
        font-size: 1.25rem;
    }
}

/* Extra small screens */
@media (max-width: 576px) {
    .section-badge {
        font-size: 12px;
        padding: 6px 16px;
    }

    .section-title {
        font-size: 1.75rem;
    }

    .decoration-line {
        width: 40px;
    }

    .modern-tool-card {
        border-radius: 16px;
    }

    .tool-card-header {
        padding: 1.25rem 1.25rem 0.75rem;
    }

    .tool-card-body {
        padding: 0 1.25rem 0.75rem;
    }

    .tool-card-footer {
        padding: 0.75rem 1.25rem 1.25rem;
    }

    .tool-icon {
        width: 50px;
        height: 50px;
        font-size: 1.25rem;
        border-radius: 12px;
    }

    .tool-badge {
        font-size: 10px;
        padding: 3px 8px;
    }

    .feature-item {
        padding: 0.375rem;
    }

    .feature-item span {
        font-size: 0.85rem;
    }

    .modern-btn {
        padding: 0.75rem 1.25rem;
        font-size: 0.85rem;
        border-radius: 12px;
    }

    .why-choose-section {
        border-radius: 16px;
        padding: 2rem 1rem;
    }

    .why-choose-section .section-title {
        font-size: 1.75rem;
    }

    .why-choose-section .section-subtitle {
        font-size: 1rem;
    }
}

/* ===================================
   ACCESSIBILITY & PERFORMANCE
   =================================== */

/* Reduce motion for users who prefer it */
@media (prefers-reduced-motion: reduce) {
    .floating-shape,
    .modern-tool-card,
    .tool-icon,
    .icon-glow,
    .modern-btn,
    .feature-highlight,
    .feature-icon {
        animation: none !important;
        transition: none !important;
    }

    .section-badge {
        animation: none !important;
    }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .modern-tool-card {
        border-width: 2px;
    }

    .tool-badge {
        border: 1px solid var(--white);
    }

    .modern-btn {
        border: 2px solid transparent;
    }
}

/* Focus styles for accessibility */
.modern-btn:focus,
.modern-tool-card:focus-within {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

/* Print styles */
@media print {
    .floating-shape,
    .tools-bg-elements {
        display: none;
    }

    .modern-tools-section {
        background: white !important;
        color: black !important;
    }

    .modern-tool-card {
        border: 1px solid #ccc;
        break-inside: avoid;
    }
}
