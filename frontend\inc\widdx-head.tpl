<!-- SEO -->
{include file="$template/frontend/inc/widdx-head-seo.tpl"}
<!-- END SEO -->

<!-- PWA Support -->
<link rel="manifest" href="{$WEB_ROOT}/templates/{$template}/manifest.json">
<meta name="theme-color" content="#4a338d">
<meta name="apple-mobile-web-app-capable" content="yes">
<meta name="apple-mobile-web-app-status-bar-style" content="black">
<meta name="apple-mobile-web-app-title" content="{$companyname}">
<link rel="apple-touch-icon" sizes="72x72"
  href="{$WEB_ROOT}/templates/{$template}/frontend/assets/img/AppImages/72.png">
<link rel="apple-touch-icon" sizes="128x128"
  href="{$WEB_ROOT}/templates/{$template}/frontend/assets/img/AppImages/128.png">
<link rel="apple-touch-icon" sizes="144x144"
  href="{$WEB_ROOT}/templates/{$template}/frontend/assets/img/AppImages/144.png">
<link rel="apple-touch-icon" sizes="152x152"
  href="{$WEB_ROOT}/templates/{$template}/frontend/assets/img/AppImages/152.png">
<link rel="apple-touch-icon" sizes="192x192"
  href="{$WEB_ROOT}/templates/{$template}/frontend/assets/img/AppImages/192.png">
<link rel="apple-touch-icon" sizes="512x512"
  href="{$WEB_ROOT}/templates/{$template}/frontend/assets/img/AppImages/512.png">

<!-- Favicon -->
<link rel="icon" href="{$WEB_ROOT}/templates/{$template}/frontend/assets/img/favicon.ico" type="image/x-icon">
<!-- END Favicon -->

<!-- Styling -->
{\WHMCS\View\Asset::fontCssInclude('open-sans-family.css')}
<link href="{assetPath file='all.min.css'}?v={$versionHash}" rel="stylesheet">
<link href="{assetPath file='theme.min.css'}?v={$versionHash}" rel="stylesheet">
<link href="{$WEB_ROOT}/templates/{$template}/frontend/assets/css/widdx-style.css" rel="stylesheet">
<link href="{$WEB_ROOT}/templates/{$template}/frontend/assets/css/theme-1.css" rel="stylesheet">
<link href="{$WEB_ROOT}/assets/css/fontawesome-all.min.css" rel="stylesheet">
<link rel="stylesheet" href="{$WEB_ROOT}/templates/{$template}/frontend/assets/css/animate.min.css">
<link rel="stylesheet" href="{$WEB_ROOT}/templates/{$template}/frontend/assets/css/owl.carousel.min.css">
<link rel="stylesheet" href="{$WEB_ROOT}/templates/{$template}/frontend/assets/css/owl.theme.default.min.css">
<link rel="stylesheet" href="{$WEB_ROOT}/templates/{$template}/frontend/assets/css/page-auth.css">
<link rel="stylesheet" href="{$WEB_ROOT}/templates/{$template}/frontend/assets/css/hosting-features.css">
<link rel="stylesheet" href="{$WEB_ROOT}/templates/{$template}/frontend/assets/css/faq-section.css">
<link rel="stylesheet" href="{$WEB_ROOT}/templates/{$template}/frontend/assets/css/dashboard-cards.css">
<link rel="stylesheet" href="{$WEB_ROOT}/templates/{$template}/frontend/assets/css/return-to-admin.css">
<!-- Theme System -->
<link rel="stylesheet" href="{$WEB_ROOT}/templates/{$template}/frontend/assets/css/theme-system.css">

<!-- Modern Homepage Styles -->
{if $templatefile == 'homepage'}
<link rel="stylesheet" href="{$WEB_ROOT}/templates/{$template}/frontend/assets/css/modern-hero.css" media="all">
<link rel="stylesheet" href="{$WEB_ROOT}/templates/{$template}/frontend/assets/css/hero-slider.css" media="all">

<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css" media="print" onload="this.media='all'">
<noscript><link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css"></noscript>
{/if}

{if ($language == 'arabic' || $language == 'hebrew' || $language == 'farsi')}
  <link href="{$WEB_ROOT}/templates/{$template}/frontend/assets/css/rtl/widdx-rtl.css" rel="stylesheet">
  <link href="{$WEB_ROOT}/templates/{$template}/frontend/assets/css/rtl/clientarea-rtl.css" rel="stylesheet">
{/if}

{assetExists file="custom.css"}
<link href="{$__assetPath__}" rel="stylesheet">
{/assetExists}

<!-- JavaScript Variables -->
<script>
  var csrfToken = '{$token}',
  markdownGuide = '{lang|addslashes key="markdown.title"}',
  locale = '{if !empty($mdeLocale)}{$mdeLocale}{else}en{/if}',
  saved = '{lang|addslashes key="markdown.saved"}',
  saving = '{lang|addslashes key="markdown.saving"}',
  whmcsBaseUrl = "{\WHMCS\Utility\Environment\WebHelper::getBaseUrl()}",
  {if $captcha}{$captcha->getPageJs()}{/if}
</script>

<!-- JavaScript Files -->
<script src="{assetPath file='scripts.min.js'}?v={$versionHash}"></script>
<!-- Theme System -->
<script src="{$WEB_ROOT}/templates/{$template}/frontend/assets/js/theme-system.js"></script>
<script src="{$WEB_ROOT}/templates/{$template}/frontend/assets/js/theme-debug.js"></script>

<!-- Modern Homepage Scripts -->
{if $templatefile == 'homepage'}
<!-- Preload critical resources for better performance -->
<link rel="preload" href="{$WEB_ROOT}/templates/{$template}/frontend/assets/img/hero-server.svg" as="image" type="image/svg+xml">
<link rel="preload" href="https://cdn.jsdelivr.net/particles.js/2.0.0/particles.min.js" as="script">

<!-- Defer non-critical JavaScript -->
<script src="https://cdn.jsdelivr.net/particles.js/2.0.0/particles.min.js" defer></script>
{/if}

<!-- Meta Tags -->
{if $templatefile == "viewticket" && !$loggedin}
  <meta name="robots" content="noindex" />
{/if}

<!-- PWA Registration -->
<script>
  if ('serviceWorker' in navigator) {
    window.addEventListener('load', function() {
      navigator.serviceWorker.register('{$WEB_ROOT}/templates/{$template}/service-worker.js')
      .then(function(registration) {
          console.log('Service Worker registered with scope:', registration.scope);
        })
        .catch(function(error) {
          console.error('Service Worker registration failed:', error);
        });
    });
  }
</script>